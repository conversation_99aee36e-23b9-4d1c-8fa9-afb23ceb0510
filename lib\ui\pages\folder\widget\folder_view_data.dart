import 'dart:ui';

import 'package:note_x/data/local/model/folder/folder_model.dart';
import 'package:note_x/gen/assets.gen.dart';

class FolderViewData {
  final String id;
  final String folderName;
  final String? parentFolderId;
  final String icon;
  final int numberOfNotes;
  final int subfoldersCount;
  final bool? notesFailed;
  final bool showOptions;
  final VoidCallback? onTap;
  final VoidCallback? onOptionsTap;
  final Function(String)? onEditFolderConfirm;
  final Function([bool?])? onDeleteConfirm;

  const FolderViewData({
    required this.id,
    required this.folderName,
    this.parentFolderId,
    required this.icon,
    required this.numberOfNotes,
    required this.subfoldersCount,
    this.notesFailed,
    this.showOptions = true,
    this.onTap,
    this.onOptionsTap,
    this.onEditFolderConfirm,
    this.onDeleteConfirm,
  });

  /// Factory constructor for unsynced notes folder
  factory FolderViewData.unsyncedNotes({
    required String folderName,
    required int numberOfNotes,
    VoidCallback? onTap,
  }) {
    return FolderViewData(
      id: '',
      folderName: folderName,
      parentFolderId: null,
      icon: Assets.icons.icFolderItem,
      numberOfNotes: numberOfNotes,
      subfoldersCount: 0,
      notesFailed: true,
      showOptions: false,
      onTap: onTap,
      // Unsynced notes don't have edit/delete callbacks
      onEditFolderConfirm: null,
      onDeleteConfirm: null,
    );
  }

  /// Factory constructor for regular folder
  factory FolderViewData.regularFolder({
    required String id,
    required String folderName,
    String? parentFolderId,
    required int numberOfNotes,
    required int subfoldersCount,
    VoidCallback? onTap,
    VoidCallback? onOptionsTap,
    Function(String)? onEditFolderConfirm,
    Function([bool?])? onDeleteConfirm,
  }) {
    return FolderViewData(
      id: id,
      folderName: folderName,
      parentFolderId: parentFolderId,
      icon: Assets.icons.icFolderItem,
      numberOfNotes: numberOfNotes,
      subfoldersCount: subfoldersCount,
      notesFailed: false,
      showOptions: true,
      onTap: onTap,
      onOptionsTap: onOptionsTap,
      onEditFolderConfirm: onEditFolderConfirm,
      onDeleteConfirm: onDeleteConfirm,
    );
  }

  /// Factory constructor from FolderModel
  factory FolderViewData.fromFolderModel({
    required FolderModel folder,
    required int numberOfNotes,
    required int subfoldersCount,
    VoidCallback? onTap,
    VoidCallback? onOptionsTap,
    Function(String)? onEditFolderConfirm,
    Function([bool?])? onDeleteConfirm,
  }) {
    return FolderViewData.regularFolder(
      id: folder.backendId,
      folderName: folder.folderName,
      parentFolderId: folder.parentFolderId,
      numberOfNotes: numberOfNotes,
      subfoldersCount: subfoldersCount,
      onTap: onTap,
      onOptionsTap: onOptionsTap,
      onEditFolderConfirm: onEditFolderConfirm,
      onDeleteConfirm: onDeleteConfirm,
    );
  }
}
