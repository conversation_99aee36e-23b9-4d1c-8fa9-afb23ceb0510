import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

class MoveToFolderHelper {
  static void showBottomSheet(
    BuildContext context, {
    List<String>? foldersToBeMovedIds,
    String? jumpToFolderId,
    List<NoteModel>? notesToBeMoved,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) {
        return BlocProvider(
          create: (context) => GetIt.instance.get<MoveFolderCubit>()
            ..initialize(jumpToFolderId: jumpToFolderId),
          child: BlocConsumer<MoveFolderCubit, MoveFolderState>(
            listener: (context, state) {
              if (state.oneShotEvent == MoveFolderOneShotEvent.folderMoved) {
                Navigator.pop(context);
              } else if (state.oneShotEvent == MoveFolderOneShotEvent.subfolderCreated) {
                Navigator.pop(context);
              } else if (state.oneShotEvent == MoveFolderOneShotEvent.error) {
                // Show error message if needed
                debugPrint('MoveFolderCubit error: ${state.errorMessage}');
              }
            },
            builder: (context, state) {
              if (state.isLoading) {
                return const SafeArea(
                  child: SizedBox(
                    height: 200,
                    child: Center(child: CircularProgressIndicator()),
                  ),
                );
              }

                return StatefulBuilder(
                  builder: (context, setState) {
                    return SafeArea(
                      child: Container(
                        padding: EdgeInsets.only(
                          left: context.isTablet ? 16 : 16.w,
                          right: context.isTablet ? 16 : 16.w,
                          top: context.isTablet ? 16 : 16.h,
                          bottom: MediaQuery.of(context).viewInsets.bottom,
                        ),
                        height: MediaQuery.of(context).size.height * 0.95,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Header
                            Row(
                              children: [
                                Expanded(
                                  child: CommonText(
                                    S.current.add_folder,
                                    style: TextStyle(
                                      fontSize: context.isTablet ? 22 : 20.sp,
                                      fontWeight: FontWeight.w600,
                                      color: context.colorScheme.mainPrimary,
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                    onTap: () => Navigator.pop(context),
                                    child: SvgPicture.asset(
                                      Assets.icons.icCloseWhite,
                                      width: context.isTablet ? 32 : 24.w,
                                      height: context.isTablet ? 32 : 24.w,
                                      fit: BoxFit.contain,
                                      colorFilter: ColorFilter.mode(
                                        context.colorScheme.mainPrimary,
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                            // Folder list
                            Expanded(
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                                  ...state.rootFolders.map((folder) => MoveFolderTile(
                                        folder: folder,
                                        onTap: (f) {
                                          context.read<MoveFolderCubit>().selectFolder(f);
                                        },
                                        onToggleExpansion: (folderId) {
                                          context.read<MoveFolderCubit>().toggleFolderExpansion(folderId);
                                        },
                                      )),
                                ],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 16.h),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: AppCommonButton(
                                      backgroundColor: context.colorScheme.mainSecondary,
                                      borderRadius: BorderRadius.circular(24.r),
                                      height: context.isTablet ? 44 : 44.h,
                                      onPressed: state.selectedFolder == null
                                          ? null
                                          : () {
                                              Navigator.pop(context);
                                              final controller = TextEditingController();
                                              showCreateFolderDialog(
                                                context,
                                                controller: controller,
                                                onPressed: () async {
                                                  await context
                                                      .read<MoveFolderCubit>()
                                                      .createSubfolder(
                                                    name: controller.text.trim(),
                                                    parentFolderId: state.selectedFolder!.backendId,
                                                  );
                                                  Navigator.pop(context);
                                                },
                                                onClosed: () {
                                                  controller.dispose();
                                                },
                                                title: S.current.create_new_folder,
                                                contentButton: S.current.create,
                                                hintText: S.current.required,
                                                initialValue: '',
                                              );
                                            },
                                      textWidget: CommonText(
                                        S.current.create,
                                        style: TextStyle(
                                          fontSize: context.isTablet ? 18 : 16.sp,
                                            color: context.colorScheme.mainPrimary,
                                            fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                  AppConstants.kSpacingItemW10,
                                  Expanded(
                                    child: AppCommonButton(
                                      backgroundColor: context.colorScheme.mainBlue,
                                      borderRadius: BorderRadius.circular(24.r),
                                      height: context.isTablet ? 44 : 44.h,
                                      onPressed: state.selectedFolder == null
                                          ? null
                                          : () async {
                                              await context
                                                  .read<MoveFolderCubit>()
                                                  .moveFoldersAndNotes(
                                                folderIds: foldersToBeMovedIds ?? [],
                                                notes: notesToBeMoved ?? [],
                                                targetFolderId: state.selectedFolder!.backendId,
                                              );
                                            },
                                      textWidget: CommonText(
                                        S.current.move,
                                        style: TextStyle(
                                          fontSize: context.isTablet ? 18 : 16.sp,
                                          color: context.colorScheme.mainPrimary,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}

class MoveFolderTile extends StatelessWidget {
  final MoveFolderViewModel folder;
  final Function(MoveFolderViewModel folder)? onTap;
  final Function(String folderId)? onToggleExpansion;
  final int level;

  const MoveFolderTile({
    Key? key,
    required this.folder,
    this.onTap,
    this.onToggleExpansion,
    this.level = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoveFolderCubit, MoveFolderState>(
      builder: (context, state) {
        final isSelected = state.selectedFolder?.backendId == folder.backendId;

        return Column(
          children: [
            ListTile(
              contentPadding: EdgeInsets.only(left: 16.w * level),
              tileColor: isSelected ? context.colorScheme.mainSecondary : null,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24.r),
              ),
              leading: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (folder.subfolders.isNotEmpty)
                    GestureDetector(
                      onTap: () => onToggleExpansion?.call(folder.backendId),
                      child: SvgPicture.asset(
                        folder.isExpanded
                            ? Assets.icons.icExpandMore
                            : Assets.icons.icExpandLess,
                        width: context.isTablet ? 24 : 16.w,
                        height: context.isTablet ? 24 : 16.w,
                        fit: BoxFit.contain,
                      ),
                    ),
                  SvgPicture.asset(
                    Assets.icons.icFlipFolderMini,
                    width: context.isTablet ? 48 : 32.w,
                    height: context.isTablet ? 48 : 32.w,
                    fit: BoxFit.contain,
                  ),
                ],
              ),
              title: Text(folder.folderName),
              onTap: () {
                onTap?.call(folder);
                if (folder.subfolders.isNotEmpty) {
                  onToggleExpansion?.call(folder.backendId);
                }
              },
            ),
            if (folder.isExpanded)
              ...folder.subfolders.map((sub) => MoveFolderTile(
                    folder: sub,
                    onTap: onTap,
                    onToggleExpansion: onToggleExpansion,
                    level: level + 1,
                  )),
          ],
        );
      },
    );
  }
}
